<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>PDF Merger</title>
  <style>
    body {
      font-family: sans-serif;
      padding: 2rem;
    }

    #dropzone {
      border: 2px dashed #ccc;
      padding: 50px;
      text-align: center;
      color: #888;
      margin-bottom: 1rem;
      cursor: pointer;
    }

    #dropzone.dragover {
      border-color: #4CAF50;
      color: #4CAF50;
    }

    button, a.button {
      padding: 0.5rem 1rem;
      font-size: 1rem;
      margin-right: 10px;
      border: none;
      background-color: #007BFF;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      display: inline-block;
    }

    #fileList {
      margin-top: 10px;
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <h2>Merge PDF Files</h2>

  <div id="dropzone">Drag & drop PDF files here or click to select</div>
  <input type="file" id="fileInput" multiple accept="application/pdf" style="display:none;" />

  <div id="fileList"></div>

  <button id="mergeBtn">Merge PDFs</button>
  <a id="downloadBtn" href="#" download="merged.pdf" class="button" style="display:none;">Download Merged PDF</a>

  <script type="module">
    import { PDFDocument } from 'https://cdn.skypack.dev/pdf-lib';

    const dropzone = document.getElementById('dropzone');
    const fileInput = document.getElementById('fileInput');
    const mergeBtn = document.getElementById('mergeBtn');
    const downloadBtn = document.getElementById('downloadBtn');
    const fileList = document.getElementById('fileList');

    let uploadedFiles = [];

    // Display uploaded files
    function updateFileList() {
      fileList.innerHTML = uploadedFiles.map(f => `<div>${f.name}</div>`).join('');
    }

    // Dropzone click opens file dialog
    dropzone.addEventListener('click', () => fileInput.click());

    // Drag over styling
    dropzone.addEventListener('dragover', e => {
      e.preventDefault();
      dropzone.classList.add('dragover');
    });

    dropzone.addEventListener('dragleave', () => {
      dropzone.classList.remove('dragover');
    });

    // Handle drop
    dropzone.addEventListener('drop', e => {
      e.preventDefault();
      dropzone.classList.remove('dragover');
      const pdfs = Array.from(e.dataTransfer.files).filter(f => f.type === 'application/pdf');
      uploadedFiles = [...uploadedFiles, ...pdfs];
      updateFileList();
    });

    // Handle file input fallback
    fileInput.addEventListener('change', e => {
      const pdfs = Array.from(e.target.files).filter(f => f.type === 'application/pdf');
      uploadedFiles = [...uploadedFiles, ...pdfs];
      updateFileList();
    });

    // Merge PDFs
    mergeBtn.addEventListener('click', async () => {
      if (uploadedFiles.length < 2) {
        alert("Please upload at least two PDF files.");
        return;
      }

      const mergedPdf = await PDFDocument.create();

      for (const file of uploadedFiles) {
        const arrayBuffer = await file.arrayBuffer();
        const pdf = await PDFDocument.load(arrayBuffer);
        const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        copiedPages.forEach(page => mergedPdf.addPage(page));
      }

      const mergedPdfBytes = await mergedPdf.save();
      const blob = new Blob([mergedPdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      downloadBtn.href = url;
      downloadBtn.style.display = 'inline-block';
    });

    // Clean up blob URL after download
    downloadBtn.addEventListener('click', () => {
      setTimeout(() => URL.revokeObjectURL(downloadBtn.href), 100);
    });
  </script>
</body>
</html>
