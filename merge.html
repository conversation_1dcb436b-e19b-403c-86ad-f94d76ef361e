<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDF Toolkit - All-in-One PDF Tools</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <style>
    :root {
      --primary-color: #6366f1;
      --secondary-color: #8b5cf6;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --error-color: #ef4444;
      --bg-color: #f8fafc;
      --card-bg: #ffffff;
      --text-primary: #1e293b;
      --text-secondary: #64748b;
      --border-color: #e2e8f0;
      --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] {
      --bg-color: #0f172a;
      --card-bg: #1e293b;
      --text-primary: #f1f5f9;
      --text-secondary: #94a3b8;
      --border-color: #334155;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background-color: var(--bg-color);
      color: var(--text-primary);
      line-height: 1.6;
      transition: all 0.2s ease;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      position: relative;
    }

    .header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 0.5rem;
    }

    .header p {
      color: var(--text-secondary);
      font-size: 1.1rem;
    }

    .theme-toggle {
      position: fixed;
      top: 2rem;
      right: 2rem;
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 50%;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: var(--shadow);
      transition: all 0.2s ease;
      z-index: 1000;
    }

    .theme-toggle:hover {
      transform: scale(1.1);
    }

    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }

    /* Page transition animations */
    #dashboard {
      opacity: 1;
      transform: translateY(0);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    #dashboard.fade-out {
      opacity: 0;
      transform: translateY(-20px);
    }

    .tool-card {
      background: var(--card-bg);
      border-radius: 12px;
      padding: 2rem;
      box-shadow: var(--shadow);
      border: 1px solid var(--border-color);
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      text-align: center;
      opacity: 0;
      transform: translateY(20px);
      animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    .tool-card:nth-child(1) { animation-delay: 0.1s; }
    .tool-card:nth-child(2) { animation-delay: 0.15s; }
    .tool-card:nth-child(3) { animation-delay: 0.2s; }
    .tool-card:nth-child(4) { animation-delay: 0.25s; }
    .tool-card:nth-child(5) { animation-delay: 0.3s; }
    .tool-card:nth-child(6) { animation-delay: 0.35s; }
    .tool-card:nth-child(7) { animation-delay: 0.4s; }
    .tool-card:nth-child(8) { animation-delay: 0.45s; }
    .tool-card:nth-child(9) { animation-delay: 0.5s; }
    .tool-card:nth-child(10) { animation-delay: 0.55s; }
    .tool-card:nth-child(11) { animation-delay: 0.6s; }
    .tool-card:nth-child(12) { animation-delay: 0.65s; }

    @keyframes fadeInUp {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .tool-card:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: var(--shadow-lg);
    }

    .tool-card:active {
      transform: translateY(-4px) scale(0.98);
    }

    .tool-card.active {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .tool-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .tool-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .tool-description {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    .workspace {
      background: var(--card-bg);
      border-radius: 12px;
      padding: 2rem;
      box-shadow: var(--shadow);
      border: 1px solid var(--border-color);
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      opacity: 0;
      transform: translateY(30px) scale(0.95);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      pointer-events: none;
      z-index: 1;
    }

    .workspace.active {
      opacity: 1;
      transform: translateY(0) scale(1);
      pointer-events: auto;
      z-index: 2;
    }

    .workspace.slide-in {
      animation: slideInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    @keyframes fadeInScale {
      from {
        opacity: 0;
        transform: scale(0.9);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }

    .workspace-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid var(--border-color);
    }

    .workspace-title {
      font-size: 1.5rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .back-btn {
      background: var(--text-secondary);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 0.5rem 1rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .back-btn:hover {
      background: var(--text-primary);
    }

    .dropzone {
      border: 2px dashed var(--border-color);
      border-radius: 12px;
      padding: 3rem;
      text-align: center;
      color: var(--text-secondary);
      margin-bottom: 2rem;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: var(--bg-color);
      opacity: 0;
      transform: translateY(20px);
      animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.2s forwards;
    }

    .dropzone:hover,
    .dropzone.dragover {
      border-color: var(--primary-color);
      background: rgba(99, 102, 241, 0.05);
      color: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: var(--shadow);
    }

    .dropzone.dragover {
      transform: scale(1.02);
      border-style: solid;
    }

    .dropzone-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .dropzone-text {
      font-size: 1.1rem;
      margin-bottom: 0.5rem;
    }

    .dropzone-subtext {
      font-size: 0.9rem;
      opacity: 0.7;
    }

    .file-list {
      margin-bottom: 2rem;
    }

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1rem;
      background: var(--bg-color);
      border-radius: 8px;
      margin-bottom: 0.5rem;
      border: 1px solid var(--border-color);
      opacity: 0;
      transform: translateX(-20px);
      animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
      transition: all 0.3s ease;
    }

    .file-item:hover {
      transform: translateX(4px);
      box-shadow: var(--shadow);
    }

    @keyframes slideInLeft {
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .file-icon {
      color: var(--error-color);
      font-size: 1.5rem;
    }

    .file-details h4 {
      font-weight: 500;
      margin-bottom: 0.25rem;
    }

    .file-details p {
      color: var(--text-secondary);
      font-size: 0.85rem;
    }

    .file-actions {
      display: flex;
      gap: 0.5rem;
    }

    .btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      position: relative;
      overflow: hidden;
    }

    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .btn:hover::before {
      left: 100%;
    }

    .btn:active {
      transform: scale(0.95);
    }

    .btn.processing {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
      }
      50% {
        box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
      }
    }

    .btn-primary {
      background: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background: #5856eb;
    }

    .btn-secondary {
      background: var(--text-secondary);
      color: white;
    }

    .btn-secondary:hover {
      background: var(--text-primary);
    }

    .btn-success {
      background: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background: #059669;
    }

    .btn-danger {
      background: var(--error-color);
      color: white;
    }

    .btn-danger:hover {
      background: #dc2626;
    }

    .btn-small {
      padding: 0.25rem 0.5rem;
      font-size: 0.8rem;
    }

    .progress-bar {
      width: 100%;
      height: 8px;
      background: var(--border-color);
      border-radius: 4px;
      overflow: hidden;
      margin: 1rem 0;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      width: 0%;
      transition: width 0.3s ease;
    }

    .hidden {
      display: none !important;
    }

    .loading {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .spinner {
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    .settings-panel {
      background: var(--bg-color);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid var(--border-color);
    }

    .settings-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
    }

    .settings-row:last-child {
      margin-bottom: 0;
    }

    .form-group {
      margin-bottom: 1rem;
    }

    .form-label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
    }

    .form-input {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      background: var(--card-bg);
      color: var(--text-primary);
      font-size: 0.9rem;
    }

    .form-input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .form-select {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      background: var(--card-bg);
      color: var(--text-primary);
      cursor: pointer;
    }

    @media (max-width: 768px) {
      .container {
        padding: 3rem 1;
      }

      .header h1 {
        font-size: 2rem;
      }

      .tools-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .tool-card {
        padding: 1.5rem;
      }

      .workspace {
        padding: 1.5rem;
      }

      .theme-toggle {
        top: 1rem;
        right: 1rem;
      }
    }
  </style>
</head>
<body>
  <div class="theme-toggle" id="themeToggle">
    <i class="fas fa-moon" id="themeIcon"></i>
  </div>

  <div class="container">
    <!-- Main Dashboard -->
    <div id="dashboard">
      <div class="header">
        <h1><i class="fas fa-file-pdf"></i> PDF Toolkit</h1>
        <p>Professional PDF tools for all your document needs</p>
      </div>

      <div class="tools-grid">
        <div class="tool-card" data-tool="merge">
          <div class="tool-icon">
            <i class="fas fa-layer-group"></i>
          </div>
          <div class="tool-title">Merge PDF</div>
          <div class="tool-description">Combine multiple PDF files into one document</div>
        </div>

        <div class="tool-card" data-tool="split">
          <div class="tool-icon">
            <i class="fas fa-cut"></i>
          </div>
          <div class="tool-title">Split PDF</div>
          <div class="tool-description">Extract pages or split PDF into multiple files</div>
        </div>

        <div class="tool-card" data-tool="organize">
          <div class="tool-icon">
            <i class="fas fa-sort"></i>
          </div>
          <div class="tool-title">Organize PDF</div>
          <div class="tool-description">Remove, reorder, and rotate PDF pages</div>
        </div>

        <div class="tool-card" data-tool="compress">
          <div class="tool-icon">
            <i class="fas fa-compress-alt"></i>
          </div>
          <div class="tool-title">Compress PDF</div>
          <div class="tool-description">Reduce PDF file size while maintaining quality</div>
        </div>

        <div class="tool-card" data-tool="pdf-to-images">
          <div class="tool-icon">
            <i class="fas fa-images"></i>
          </div>
          <div class="tool-title">PDF to Images</div>
          <div class="tool-description">Convert PDF pages to JPG or PNG images</div>
        </div>

        <div class="tool-card" data-tool="images-to-pdf">
          <div class="tool-icon">
            <i class="fas fa-file-image"></i>
          </div>
          <div class="tool-title">Images to PDF</div>
          <div class="tool-description">Convert images to a single PDF document</div>
        </div>

        <div class="tool-card" data-tool="protect">
          <div class="tool-icon">
            <i class="fas fa-lock"></i>
          </div>
          <div class="tool-title">Protect PDF</div>
          <div class="tool-description">Add password protection to your PDF</div>
        </div>

        <div class="tool-card" data-tool="unlock">
          <div class="tool-icon">
            <i class="fas fa-unlock"></i>
          </div>
          <div class="tool-title">Unlock PDF</div>
          <div class="tool-description">Remove password protection from PDF</div>
        </div>

        <div class="tool-card" data-tool="watermark">
          <div class="tool-icon">
            <i class="fas fa-stamp"></i>
          </div>
          <div class="tool-title">Add Watermark</div>
          <div class="tool-description">Add text or image watermarks to PDF</div>
        </div>

        <div class="tool-card" data-tool="rotate">
          <div class="tool-icon">
            <i class="fas fa-redo"></i>
          </div>
          <div class="tool-title">Rotate PDF</div>
          <div class="tool-description">Rotate PDF pages to correct orientation</div>
        </div>

        <div class="tool-card" data-tool="page-numbers">
          <div class="tool-icon">
            <i class="fas fa-list-ol"></i>
          </div>
          <div class="tool-title">Page Numbers</div>
          <div class="tool-description">Add page numbers to your PDF document</div>
        </div>

        <div class="tool-card" data-tool="crop">
          <div class="tool-icon">
            <i class="fas fa-crop"></i>
          </div>
          <div class="tool-title">Crop PDF</div>
          <div class="tool-description">Crop PDF pages to specific dimensions</div>
        </div>
      </div>
    </div>

    <!-- Workspaces for each tool -->
    <div id="workspace-merge" class="workspace">
      <div class="workspace-header">
        <div class="workspace-title">
          <i class="fas fa-layer-group"></i>
          Merge PDF Files
        </div>
        <button class="back-btn" onclick="showDashboard()">
          <i class="fas fa-arrow-left"></i> Back
        </button>
      </div>

      <div class="dropzone" id="merge-dropzone">
        <div class="dropzone-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <div class="dropzone-text">Drag & drop PDF files here</div>
        <div class="dropzone-subtext">or click to select files</div>
      </div>

      <input type="file" id="merge-file-input" multiple accept="application/pdf" style="display: none;">

      <div id="merge-file-list" class="file-list"></div>

      <div class="settings-panel" id="merge-settings">
        <h3>Merge Settings</h3>
        <div class="settings-row">
          <label class="form-label">Output filename:</label>
          <input type="text" class="form-input" id="merge-filename" value="merged.pdf" style="width: 200px;">
        </div>
      </div>

      <div class="progress-bar hidden" id="merge-progress">
        <div class="progress-fill"></div>
      </div>

      <button class="btn btn-primary" id="merge-process-btn" disabled>
        <i class="fas fa-layer-group"></i>
        Merge PDFs
      </button>
      <a class="btn btn-success hidden" id="merge-download-btn" download="merged.pdf">
        <i class="fas fa-download"></i>
        Download Merged PDF
      </a>
    </div>

    <!-- Split PDF Workspace -->
    <div id="workspace-split" class="workspace">
      <div class="workspace-header">
        <div class="workspace-title">
          <i class="fas fa-cut"></i>
          Split PDF
        </div>
        <button class="back-btn" onclick="showDashboard()">
          <i class="fas fa-arrow-left"></i> Back
        </button>
      </div>

      <div class="dropzone" id="split-dropzone">
        <div class="dropzone-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <div class="dropzone-text">Drag & drop a PDF file here</div>
        <div class="dropzone-subtext">or click to select a file</div>
      </div>

      <input type="file" id="split-file-input" accept="application/pdf" style="display: none;">

      <div id="split-file-list" class="file-list"></div>

      <div class="settings-panel" id="split-settings">
        <h3>Split Options</h3>
        <div class="form-group">
          <label class="form-label">Split method:</label>
          <select class="form-select" id="split-method">
            <option value="pages">Extract specific pages</option>
            <option value="range">Split by page ranges</option>
            <option value="every">Split every N pages</option>
          </select>
        </div>
        <div class="form-group" id="split-pages-input">
          <label class="form-label">Pages to extract (e.g., 1,3,5-8):</label>
          <input type="text" class="form-input" id="split-pages" placeholder="1,3,5-8">
        </div>
        <div class="form-group hidden" id="split-every-input">
          <label class="form-label">Split every N pages:</label>
          <input type="number" class="form-input" id="split-every" value="1" min="1">
        </div>
      </div>

      <div class="progress-bar hidden" id="split-progress">
        <div class="progress-fill"></div>
      </div>

      <button class="btn btn-primary" id="split-process-btn" disabled>
        <i class="fas fa-cut"></i>
        Split PDF
      </button>
      <div id="split-download-area"></div>
    </div>

    <!-- Organize PDF Workspace -->
    <div id="workspace-organize" class="workspace">
      <div class="workspace-header">
        <div class="workspace-title">
          <i class="fas fa-sort"></i>
          Organize PDF
        </div>
        <button class="back-btn" onclick="showDashboard()">
          <i class="fas fa-arrow-left"></i> Back
        </button>
      </div>

      <div class="dropzone" id="organize-dropzone">
        <div class="dropzone-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <div class="dropzone-text">Drag & drop a PDF file here</div>
        <div class="dropzone-subtext">or click to select a file</div>
      </div>

      <input type="file" id="organize-file-input" accept="application/pdf" style="display: none;">

      <div id="organize-file-list" class="file-list"></div>

      <div id="organize-pages-container" class="hidden">
        <h3>PDF Pages</h3>
        <div id="organize-pages-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 2rem;"></div>
      </div>

      <div class="progress-bar hidden" id="organize-progress">
        <div class="progress-fill"></div>
      </div>

      <button class="btn btn-primary" id="organize-process-btn" disabled>
        <i class="fas fa-sort"></i>
        Apply Changes
      </button>
      <a class="btn btn-success hidden" id="organize-download-btn" download="organized.pdf">
        <i class="fas fa-download"></i>
        Download Organized PDF
      </a>
    </div>

    <!-- Compress PDF Workspace -->
    <div id="workspace-compress" class="workspace">
      <div class="workspace-header">
        <div class="workspace-title">
          <i class="fas fa-compress-alt"></i>
          Compress PDF
        </div>
        <button class="back-btn" onclick="showDashboard()">
          <i class="fas fa-arrow-left"></i> Back
        </button>
      </div>

      <div class="dropzone" id="compress-dropzone">
        <div class="dropzone-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <div class="dropzone-text">Drag & drop a PDF file here</div>
        <div class="dropzone-subtext">or click to select a file</div>
      </div>

      <input type="file" id="compress-file-input" accept="application/pdf" style="display: none;">

      <div id="compress-file-list" class="file-list"></div>

      <div class="settings-panel" id="compress-settings">
        <h3>Compression Settings</h3>
        <div class="form-group">
          <label class="form-label">Compression level:</label>
          <select class="form-select" id="compress-level">
            <option value="low">Low compression (better quality)</option>
            <option value="medium" selected>Medium compression</option>
            <option value="high">High compression (smaller size)</option>
          </select>
        </div>
      </div>

      <div class="progress-bar hidden" id="compress-progress">
        <div class="progress-fill"></div>
      </div>

      <button class="btn btn-primary" id="compress-process-btn" disabled>
        <i class="fas fa-compress-alt"></i>
        Compress PDF
      </button>
      <a class="btn btn-success hidden" id="compress-download-btn" download="compressed.pdf">
        <i class="fas fa-download"></i>
        Download Compressed PDF
      </a>
    </div>

    <!-- Images to PDF Workspace -->
    <div id="workspace-images-to-pdf" class="workspace">
      <div class="workspace-header">
        <div class="workspace-title">
          <i class="fas fa-file-image"></i>
          Images to PDF
        </div>
        <button class="back-btn" onclick="showDashboard()">
          <i class="fas fa-arrow-left"></i> Back
        </button>
      </div>

      <div class="dropzone" id="images-dropzone">
        <div class="dropzone-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <div class="dropzone-text">Drag & drop image files here</div>
        <div class="dropzone-subtext">Supports JPG, PNG, GIF, BMP</div>
      </div>

      <input type="file" id="images-file-input" multiple accept="image/*" style="display: none;">

      <div id="images-file-list" class="file-list"></div>

      <div class="settings-panel" id="images-settings">
        <h3>PDF Settings</h3>
        <div class="form-group">
          <label class="form-label">Page size:</label>
          <select class="form-select" id="images-page-size">
            <option value="a4">A4</option>
            <option value="letter">Letter</option>
            <option value="legal">Legal</option>
            <option value="auto">Auto (fit to image)</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label">Image quality:</label>
          <select class="form-select" id="images-quality">
            <option value="high">High quality</option>
            <option value="medium" selected>Medium quality</option>
            <option value="low">Low quality (smaller size)</option>
          </select>
        </div>
      </div>

      <div class="progress-bar hidden" id="images-progress">
        <div class="progress-fill"></div>
      </div>

      <button class="btn btn-primary" id="images-process-btn" disabled>
        <i class="fas fa-file-image"></i>
        Create PDF
      </button>
      <a class="btn btn-success hidden" id="images-download-btn" download="images.pdf">
        <i class="fas fa-download"></i>
        Download PDF
      </a>
    </div>
  </div>

  <script type="module">
    // Import required libraries
    import { PDFDocument, rgb, StandardFonts, PageSizes } from 'https://cdn.skypack.dev/pdf-lib';

    // Global state
    let currentTool = null;
    let uploadedFiles = {};

    // Theme management
    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = document.getElementById('themeIcon');
    const body = document.body;

    // Initialize theme
    const savedTheme = localStorage.getItem('theme') || 'light';
    if (savedTheme === 'dark') {
      body.setAttribute('data-theme', 'dark');
      themeIcon.className = 'fas fa-sun';
    }

    themeToggle.addEventListener('click', () => {
      const currentTheme = body.getAttribute('data-theme');
      if (currentTheme === 'dark') {
        body.removeAttribute('data-theme');
        themeIcon.className = 'fas fa-moon';
        localStorage.setItem('theme', 'light');
      } else {
        body.setAttribute('data-theme', 'dark');
        themeIcon.className = 'fas fa-sun';
        localStorage.setItem('theme', 'dark');
      }
    });

    // Navigation functions
    window.showDashboard = function() {
      document.getElementById('dashboard').style.display = 'block';
      document.querySelectorAll('.workspace').forEach(ws => {
        ws.classList.remove('active');
      });
      currentTool = null;
    };

    function showWorkspace(toolName) {
      const workspace = document.getElementById(`workspace-${toolName}`);

      // Check if workspace exists
      if (!workspace) {
        showError(`${toolName.charAt(0).toUpperCase() + toolName.slice(1)} tool is coming soon! Currently available: Merge, Split, Compress, and Images to PDF.`);
        return;
      }

      document.getElementById('dashboard').style.display = 'none';
      document.querySelectorAll('.workspace').forEach(ws => {
        ws.classList.remove('active');
      });
      workspace.classList.add('active');
      currentTool = toolName;
    }

    // Tool card click handlers
    document.querySelectorAll('.tool-card').forEach(card => {
      card.addEventListener('click', () => {
        const tool = card.getAttribute('data-tool');
        showWorkspace(tool);
      });
    });

    // Utility functions
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function showProgress(toolName, show = true) {
      const progressBar = document.getElementById(`${toolName}-progress`);
      if (show) {
        progressBar.classList.remove('hidden');
      } else {
        progressBar.classList.add('hidden');
      }
    }

    function updateProgress(toolName, percent) {
      const progressFill = document.querySelector(`#${toolName}-progress .progress-fill`);
      progressFill.style.width = `${percent}%`;
    }

    function showError(message) {
      // Create a simple toast notification
      const toast = document.createElement('div');
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--error-color);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        max-width: 400px;
        font-weight: 500;
      `;
      toast.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
      document.body.appendChild(toast);

      setTimeout(() => {
        toast.remove();
      }, 5000);
    }

    function showSuccess(message) {
      // Create a simple toast notification
      const toast = document.createElement('div');
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--success-color);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        max-width: 400px;
        font-weight: 500;
      `;
      toast.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
      document.body.appendChild(toast);

      setTimeout(() => {
        toast.remove();
      }, 4000);
    }

    // File handling utilities
    function createFileItem(file, toolName, index) {
      const fileItem = document.createElement('div');
      fileItem.className = 'file-item';
      fileItem.style.animationDelay = `${index * 0.05}s`;
      fileItem.innerHTML = `
        <div class="file-info">
          <div class="file-icon">
            <i class="fas ${file.type.includes('pdf') ? 'fa-file-pdf' : 'fa-file-image'}"></i>
          </div>
          <div class="file-details">
            <h4>${file.name}</h4>
            <p>${formatFileSize(file.size)}</p>
          </div>
        </div>
        <div class="file-actions">
          <button class="btn btn-danger btn-small" onclick="removeFile('${toolName}', ${index})">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      `;
      return fileItem;
    }

    window.removeFile = function(toolName, index) {
      uploadedFiles[toolName].splice(index, 1);
      updateFileList(toolName);
      updateProcessButton(toolName);
    };

    function updateFileList(toolName) {
      const fileList = document.getElementById(`${toolName}-file-list`);
      fileList.innerHTML = '';

      if (uploadedFiles[toolName] && uploadedFiles[toolName].length > 0) {
        uploadedFiles[toolName].forEach((file, index) => {
          fileList.appendChild(createFileItem(file, toolName, index));
        });
      }
    }

    function updateProcessButton(toolName) {
      const processBtn = document.getElementById(`${toolName}-process-btn`);
      const files = uploadedFiles[toolName] || [];

      let shouldEnable = false;
      switch (toolName) {
        case 'merge':
          shouldEnable = files.length >= 2;
          break;
        case 'images-to-pdf':
          shouldEnable = files.length >= 1;
          break;
        default:
          shouldEnable = files.length === 1;
      }

      processBtn.disabled = !shouldEnable;
    }

    // Generic dropzone setup
    function setupDropzone(toolName, acceptTypes = 'application/pdf', multiple = false) {
      const dropzone = document.getElementById(`${toolName}-dropzone`);
      const fileInput = document.getElementById(`${toolName}-file-input`);

      // Check if elements exist before setting up event listeners
      if (!dropzone || !fileInput) {
        console.warn(`Dropzone elements not found for tool: ${toolName}`);
        return;
      }

      if (!uploadedFiles[toolName]) {
        uploadedFiles[toolName] = [];
      }

      // Dropzone click opens file dialog
      dropzone.addEventListener('click', () => fileInput.click());

      // Drag over styling
      dropzone.addEventListener('dragover', e => {
        e.preventDefault();
        dropzone.classList.add('dragover');
      });

      dropzone.addEventListener('dragleave', () => {
        dropzone.classList.remove('dragover');
      });

      // Handle drop
      dropzone.addEventListener('drop', e => {
        e.preventDefault();
        dropzone.classList.remove('dragover');

        const files = Array.from(e.dataTransfer.files).filter(f => {
          if (acceptTypes === 'application/pdf') {
            return f.type === 'application/pdf';
          } else if (acceptTypes === 'image/*') {
            return f.type.startsWith('image/');
          }
          return true;
        });

        if (multiple) {
          uploadedFiles[toolName] = [...uploadedFiles[toolName], ...files];
        } else {
          uploadedFiles[toolName] = files.slice(0, 1);
        }

        updateFileList(toolName);
        updateProcessButton(toolName);
      });

      // Handle file input
      fileInput.addEventListener('change', e => {
        const files = Array.from(e.target.files).filter(f => {
          if (acceptTypes === 'application/pdf') {
            return f.type === 'application/pdf';
          } else if (acceptTypes === 'image/*') {
            return f.type.startsWith('image/');
          }
          return true;
        });

        if (multiple) {
          uploadedFiles[toolName] = [...uploadedFiles[toolName], ...files];
        } else {
          uploadedFiles[toolName] = files.slice(0, 1);
        }

        updateFileList(toolName);
        updateProcessButton(toolName);
      });
    }

    // Setup dropzones for implemented tools only
    setupDropzone('merge', 'application/pdf', true);
    setupDropzone('split', 'application/pdf', false);
    setupDropzone('compress', 'application/pdf', false);
    setupDropzone('images-to-pdf', 'image/*', true);

    // Note: organize and other tools will be set up when their workspaces are implemented

    // ===== MERGE PDF FUNCTIONALITY =====
    document.getElementById('merge-process-btn').addEventListener('click', async () => {
      const files = uploadedFiles['merge'];
      if (files.length < 2) {
        showError('Please upload at least two PDF files to merge.');
        return;
      }

      try {
        showProgress('merge');
        updateProgress('merge', 10);

        const mergedPdf = await PDFDocument.create();

        for (let i = 0; i < files.length; i++) {
          updateProgress('merge', 10 + (i / files.length) * 80);

          const arrayBuffer = await files[i].arrayBuffer();
          const pdf = await PDFDocument.load(arrayBuffer);
          const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
          copiedPages.forEach(page => mergedPdf.addPage(page));
        }

        updateProgress('merge', 95);
        const mergedPdfBytes = await mergedPdf.save();
        const blob = new Blob([mergedPdfBytes], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);

        const downloadBtn = document.getElementById('merge-download-btn');
        const filename = document.getElementById('merge-filename').value || 'merged.pdf';
        downloadBtn.href = url;
        downloadBtn.download = filename;
        downloadBtn.classList.remove('hidden');

        updateProgress('merge', 100);
        setTimeout(() => showProgress('merge', false), 1000);
        showSuccess('PDF files merged successfully!');

      } catch (error) {
        showProgress('merge', false);
        showError('Error merging PDFs: ' + error.message);
      }
    });

    // ===== SPLIT PDF FUNCTIONALITY =====
    document.getElementById('split-process-btn').addEventListener('click', async () => {
      const files = uploadedFiles['split'];
      if (files.length !== 1) {
        showError('Please upload exactly one PDF file to split.');
        return;
      }

      try {
        showProgress('split');
        updateProgress('split', 10);

        const arrayBuffer = await files[0].arrayBuffer();
        const pdf = await PDFDocument.load(arrayBuffer);
        const totalPages = pdf.getPageCount();

        const splitMethod = document.getElementById('split-method').value;
        let pagesToExtract = [];

        if (splitMethod === 'pages') {
          const pagesInput = document.getElementById('split-pages').value;
          pagesToExtract = parsePageNumbers(pagesInput, totalPages);
        } else if (splitMethod === 'every') {
          const everyN = parseInt(document.getElementById('split-every').value) || 1;
          for (let i = 0; i < totalPages; i += everyN) {
            pagesToExtract.push(Array.from({length: Math.min(everyN, totalPages - i)}, (_, j) => i + j));
          }
        }

        updateProgress('split', 30);

        const downloadArea = document.getElementById('split-download-area');
        downloadArea.innerHTML = '';

        for (let i = 0; i < pagesToExtract.length; i++) {
          updateProgress('split', 30 + (i / pagesToExtract.length) * 60);

          const newPdf = await PDFDocument.create();
          const pages = Array.isArray(pagesToExtract[i]) ? pagesToExtract[i] : [pagesToExtract[i]];

          const copiedPages = await newPdf.copyPages(pdf, pages);
          copiedPages.forEach(page => newPdf.addPage(page));

          const pdfBytes = await newPdf.save();
          const blob = new Blob([pdfBytes], { type: 'application/pdf' });
          const url = URL.createObjectURL(blob);

          const downloadBtn = document.createElement('a');
          downloadBtn.href = url;
          downloadBtn.download = `split_${i + 1}.pdf`;
          downloadBtn.className = 'btn btn-success';
          downloadBtn.innerHTML = `<i class="fas fa-download"></i> Download Part ${i + 1}`;
          downloadBtn.style.marginRight = '10px';
          downloadBtn.style.marginBottom = '10px';
          downloadArea.appendChild(downloadBtn);
        }

        updateProgress('split', 100);
        setTimeout(() => showProgress('split', false), 1000);
        showSuccess('PDF split successfully!');

      } catch (error) {
        showProgress('split', false);
        showError('Error splitting PDF: ' + error.message);
      }
    });

    // ===== IMAGES TO PDF FUNCTIONALITY =====
    document.getElementById('images-process-btn').addEventListener('click', async () => {
      const files = uploadedFiles['images-to-pdf'];
      if (files.length === 0) {
        showError('Please upload at least one image file.');
        return;
      }

      try {
        showProgress('images-to-pdf');
        updateProgress('images-to-pdf', 10);

        const pdfDoc = await PDFDocument.create();
        const pageSize = document.getElementById('images-page-size').value;

        for (let i = 0; i < files.length; i++) {
          updateProgress('images-to-pdf', 10 + (i / files.length) * 80);

          const imageBytes = await files[i].arrayBuffer();
          let image;

          if (files[i].type === 'image/jpeg' || files[i].type === 'image/jpg') {
            image = await pdfDoc.embedJpg(imageBytes);
          } else if (files[i].type === 'image/png') {
            image = await pdfDoc.embedPng(imageBytes);
          } else {
            // Convert other formats to PNG first (simplified approach)
            continue;
          }

          let page;
          if (pageSize === 'auto') {
            page = pdfDoc.addPage([image.width, image.height]);
          } else {
            const sizes = {
              'a4': PageSizes.A4,
              'letter': PageSizes.Letter,
              'legal': PageSizes.Legal
            };
            page = pdfDoc.addPage(sizes[pageSize]);
          }

          const { width, height } = page.getSize();
          const imageAspectRatio = image.width / image.height;
          const pageAspectRatio = width / height;

          let imageWidth, imageHeight;
          if (imageAspectRatio > pageAspectRatio) {
            imageWidth = width;
            imageHeight = width / imageAspectRatio;
          } else {
            imageHeight = height;
            imageWidth = height * imageAspectRatio;
          }

          page.drawImage(image, {
            x: (width - imageWidth) / 2,
            y: (height - imageHeight) / 2,
            width: imageWidth,
            height: imageHeight,
          });
        }

        updateProgress('images-to-pdf', 95);
        const pdfBytes = await pdfDoc.save();
        const blob = new Blob([pdfBytes], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);

        const downloadBtn = document.getElementById('images-download-btn');
        downloadBtn.href = url;
        downloadBtn.classList.remove('hidden');

        updateProgress('images-to-pdf', 100);
        setTimeout(() => showProgress('images-to-pdf', false), 1000);
        showSuccess('Images converted to PDF successfully!');

      } catch (error) {
        showProgress('images-to-pdf', false);
        showError('Error converting images to PDF: ' + error.message);
      }
    });

    // ===== COMPRESS PDF FUNCTIONALITY =====
    document.getElementById('compress-process-btn').addEventListener('click', async () => {
      const files = uploadedFiles['compress'];
      if (files.length !== 1) {
        showError('Please upload exactly one PDF file to compress.');
        return;
      }

      try {
        showProgress('compress');
        updateProgress('compress', 10);

        const arrayBuffer = await files[0].arrayBuffer();
        const pdf = await PDFDocument.load(arrayBuffer);

        updateProgress('compress', 50);

        // Basic compression by re-saving the PDF
        // Note: pdf-lib doesn't have advanced compression features
        // In a real app, you'd use a more sophisticated compression library
        const compressedBytes = await pdf.save({
          useObjectStreams: false,
          addDefaultPage: false,
        });

        updateProgress('compress', 90);

        const blob = new Blob([compressedBytes], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);

        const downloadBtn = document.getElementById('compress-download-btn');
        downloadBtn.href = url;
        downloadBtn.classList.remove('hidden');

        const originalSize = files[0].size;
        const compressedSize = compressedBytes.length;
        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

        updateProgress('compress', 100);
        setTimeout(() => showProgress('compress', false), 1000);
        showSuccess(`PDF compressed successfully! Size reduced by ${compressionRatio}%`);

      } catch (error) {
        showProgress('compress', false);
        showError('Error compressing PDF: ' + error.message);
      }
    });

    // Utility function to parse page numbers
    function parsePageNumbers(input, totalPages) {
      const pages = [];
      const parts = input.split(',');

      for (const part of parts) {
        const trimmed = part.trim();
        if (trimmed.includes('-')) {
          const [start, end] = trimmed.split('-').map(n => parseInt(n.trim()));
          for (let i = Math.max(1, start); i <= Math.min(totalPages, end); i++) {
            pages.push(i - 1); // Convert to 0-based index
          }
        } else {
          const pageNum = parseInt(trimmed);
          if (pageNum >= 1 && pageNum <= totalPages) {
            pages.push(pageNum - 1); // Convert to 0-based index
          }
        }
      }

      return [...new Set(pages)].sort((a, b) => a - b); // Remove duplicates and sort
    }

    // Split method change handler
    document.getElementById('split-method').addEventListener('change', (e) => {
      const method = e.target.value;
      const pagesInput = document.getElementById('split-pages-input');
      const everyInput = document.getElementById('split-every-input');

      if (method === 'pages') {
        pagesInput.classList.remove('hidden');
        everyInput.classList.add('hidden');
      } else if (method === 'every') {
        pagesInput.classList.add('hidden');
        everyInput.classList.remove('hidden');
      } else {
        pagesInput.classList.add('hidden');
        everyInput.classList.add('hidden');
      }
    });
  </script>
</body>
</html>
